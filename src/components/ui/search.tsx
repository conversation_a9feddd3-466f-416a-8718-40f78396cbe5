import * as React from "react";
import { Search, X, Clock, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

export interface SearchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  placeholder?: string;
  className?: string;
  
  // Compact mode - shows as search icon that expands
  compact?: boolean;
  
  // Loading state
  loading?: boolean;
  
  // Suggestions/recent searches
  suggestions?: string[];
  recentSearches?: string[];
  showSuggestions?: boolean;
  
  // Debouncing
  debounceMs?: number;
  
  // Size variants
  size?: "sm" | "md" | "lg";
  
  // Clear functionality
  clearable?: boolean;
  
  // Auto focus when expanded (for compact mode)
  autoFocus?: boolean;
}

const Search = React.forwardRef<HTMLInputElement, SearchProps>(
  ({
    value = "",
    onChange,
    onSearch,
    placeholder = "Search...",
    className,
    compact = false,
    loading = false,
    suggestions = [],
    recentSearches = [],
    showSuggestions = true,
    debounceMs = 300,
    size = "md",
    clearable = true,
    autoFocus = false,
    ...props
  }, ref) => {
    const [localValue, setLocalValue] = React.useState(value);
    const [isExpanded, setIsExpanded] = React.useState(!compact);
    const [isSuggestionsOpen, setIsSuggestionsOpen] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);
    
    const inputRef = React.useRef<HTMLInputElement>(null);
    const debounceRef = React.useRef<NodeJS.Timeout>();
    
    // Combine refs
    React.useImperativeHandle(ref, () => inputRef.current!);
    
    // Sync external value changes
    React.useEffect(() => {
      setLocalValue(value);
    }, [value]);
    
    // Debounced onChange
    React.useEffect(() => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
      
      debounceRef.current = setTimeout(() => {
        onChange?.(localValue);
      }, debounceMs);
      
      return () => {
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }
      };
    }, [localValue, onChange, debounceMs]);
    
    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setLocalValue(newValue);
      
      // Show suggestions when typing
      if (showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)) {
        setIsSuggestionsOpen(true);
      }
    };
    
    // Handle search submission
    const handleSearch = (searchValue?: string) => {
      const valueToSearch = searchValue || localValue;
      onSearch?.(valueToSearch);
      setIsSuggestionsOpen(false);
      inputRef.current?.blur();
    };
    
    // Handle key press
    const handleKeyPress = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleSearch();
      } else if (e.key === 'Escape') {
        setIsSuggestionsOpen(false);
        inputRef.current?.blur();
      }
    };
    
    // Handle clear
    const handleClear = () => {
      setLocalValue("");
      onChange?.("");
      inputRef.current?.focus();
    };
    
    // Handle compact mode expansion
    const handleExpand = () => {
      setIsExpanded(true);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    };
    
    // Handle compact mode collapse
    const handleCollapse = () => {
      if (compact && !localValue) {
        setIsExpanded(false);
        setIsSuggestionsOpen(false);
      }
    };
    
    // Handle focus
    const handleFocus = () => {
      setIsFocused(true);
      if (showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)) {
        setIsSuggestionsOpen(true);
      }
    };
    
    // Handle blur
    const handleBlur = () => {
      setIsFocused(false);
      setTimeout(() => {
        setIsSuggestionsOpen(false);
        handleCollapse();
      }, 200); // Delay to allow suggestion clicks
    };
    
    // Size classes
    const sizeClasses = {
      sm: "h-8 text-sm",
      md: "h-9 text-sm", 
      lg: "h-10 text-base"
    };
    
    // If compact and not expanded, show search icon
    if (compact && !isExpanded) {
      return (
        <Button
          variant="ghost"
          size="icon"
          onClick={handleExpand}
          className={cn("shrink-0", sizeClasses[size], className)}
          aria-label="Search"
        >
          <Search className="h-4 w-4" />
        </Button>
      );
    }
    
    // All suggestions combined
    const allSuggestions = [
      ...recentSearches.map(search => ({ type: 'recent', value: search })),
      ...suggestions.map(suggestion => ({ type: 'suggestion', value: suggestion }))
    ].filter(item => 
      localValue === "" || 
      item.value.toLowerCase().includes(localValue.toLowerCase())
    );
    
    const searchInput = (
      <div className={cn("relative flex items-center", className)}>
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            ref={inputRef}
            type="text"
            value={localValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyPress}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            className={cn(
              "pl-8 pr-8",
              sizeClasses[size],
              clearable && localValue && "pr-16"
            )}
            autoFocus={autoFocus && isExpanded}
            {...props}
          />
          
          {/* Loading indicator */}
          {loading && (
            <div className="absolute right-8 top-1/2 -translate-y-1/2">
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            </div>
          )}
          
          {/* Clear button */}
          {clearable && localValue && !loading && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleClear}
              className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        {/* Compact mode collapse button */}
        {compact && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={handleCollapse}
            className="ml-1 h-6 w-6 text-muted-foreground hover:text-foreground"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
    
    // If no suggestions, return simple input
    if (!showSuggestions || allSuggestions.length === 0) {
      return searchInput;
    }
    
    // Return input with suggestions popover
    return (
      <Popover open={isSuggestionsOpen} onOpenChange={setIsSuggestionsOpen}>
        <PopoverTrigger asChild>
          {searchInput}
        </PopoverTrigger>
        <PopoverContent 
          className="w-[var(--radix-popover-trigger-width)] p-0" 
          align="start"
          side="bottom"
        >
          <Command>
            <CommandList>
              <CommandEmpty>No suggestions found.</CommandEmpty>
              
              {recentSearches.length > 0 && (
                <CommandGroup heading="Recent Searches">
                  {recentSearches
                    .filter(search => 
                      localValue === "" || 
                      search.toLowerCase().includes(localValue.toLowerCase())
                    )
                    .slice(0, 5)
                    .map((search, index) => (
                      <CommandItem
                        key={`recent-${index}`}
                        value={search}
                        onSelect={() => handleSearch(search)}
                        className="flex items-center gap-2"
                      >
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span>{search}</span>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}
              
              {suggestions.length > 0 && (
                <CommandGroup heading="Suggestions">
                  {suggestions
                    .filter(suggestion => 
                      localValue === "" || 
                      suggestion.toLowerCase().includes(localValue.toLowerCase())
                    )
                    .slice(0, 5)
                    .map((suggestion, index) => (
                      <CommandItem
                        key={`suggestion-${index}`}
                        value={suggestion}
                        onSelect={() => handleSearch(suggestion)}
                        className="flex items-center gap-2"
                      >
                        <Search className="h-3 w-3 text-muted-foreground" />
                        <span>{suggestion}</span>
                      </CommandItem>
                    ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }
);

Search.displayName = "Search";

export { Search };
