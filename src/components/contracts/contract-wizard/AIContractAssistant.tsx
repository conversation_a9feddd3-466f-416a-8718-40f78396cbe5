import React, { useState, useEffect, useCallback } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  Shield,
  FileText,
  Zap,
  RefreshCw,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Plus
} from 'lucide-react';

interface AISuggestion {
  id: string;
  type: 'clause' | 'term' | 'risk' | 'compliance' | 'optimization';
  title: string;
  description: string;
  content?: string;
  confidence: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  reasoning: string;
  applicable: boolean;
}

interface AIAnalysis {
  riskScore: number;
  complianceScore: number;
  suggestions: AISuggestion[];
  missingClauses: string[];
  potentialIssues: string[];
  recommendations: string[];
}

/**
 * AI-Powered Contract Assistant
 * - Provides intelligent suggestions for clauses, terms, and improvements
 * - Analyzes contract for risks and compliance issues
 * - Offers industry-specific recommendations
 * - Integrates with LegalBERT for advanced analysis
 */
const AIContractAssistant: React.FC = () => {
  const { data } = useContractWizard();
  const [analysis, setAnalysis] = useState<AIAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<AISuggestion | null>(null);
  const [customPrompt, setCustomPrompt] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Mock AI analysis function (will be replaced with actual LegalBERT integration)
  const performAIAnalysis = useCallback(async () => {
    setIsAnalyzing(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock analysis based on contract data
    const mockAnalysis: AIAnalysis = {
      riskScore: calculateRiskScore(),
      complianceScore: calculateComplianceScore(),
      suggestions: generateSuggestions(),
      missingClauses: findMissingClauses(),
      potentialIssues: identifyIssues(),
      recommendations: generateRecommendations()
    };
    
    setAnalysis(mockAnalysis);
    setIsAnalyzing(false);
  }, [data]);

  const calculateRiskScore = (): number => {
    let score = 85; // Base score
    
    // Adjust based on contract complexity
    if (!data.paymentTerms) score -= 10;
    if (!data.contractValue) score -= 5;
    if (data.standardClauses.length < 3) score -= 15;
    if (!data.jurisdiction) score -= 10;
    
    return Math.max(0, Math.min(100, score));
  };

  const calculateComplianceScore = (): number => {
    let score = 90; // Base score
    
    // Industry-specific compliance checks
    if (data.industry === 'healthcare' && !data.standardClauses.includes('HIPAA Compliance')) {
      score -= 20;
    }
    if (data.industry === 'finance' && !data.standardClauses.includes('Financial Regulations')) {
      score -= 15;
    }
    
    return Math.max(0, Math.min(100, score));
  };

  const generateSuggestions = (): AISuggestion[] => {
    const suggestions: AISuggestion[] = [];
    
    // Payment terms suggestion
    if (!data.paymentTerms) {
      suggestions.push({
        id: 'payment-terms',
        type: 'term',
        title: 'Add Payment Terms',
        description: 'Specify clear payment terms to avoid disputes',
        content: 'Payment shall be due within 30 days of invoice receipt.',
        confidence: 95,
        priority: 'high',
        category: 'Financial Terms',
        reasoning: 'Clear payment terms reduce the risk of payment disputes and improve cash flow predictability.',
        applicable: true
      });
    }

    // Termination clause suggestion
    if (!data.standardClauses.includes('Termination')) {
      suggestions.push({
        id: 'termination-clause',
        type: 'clause',
        title: 'Add Termination Clause',
        description: 'Include provisions for contract termination',
        content: 'Either party may terminate this Agreement with 30 days written notice.',
        confidence: 90,
        priority: 'high',
        category: 'Contract Management',
        reasoning: 'Termination clauses provide flexibility and clear exit strategies for both parties.',
        applicable: true
      });
    }

    // Industry-specific suggestions
    if (data.industry === 'technology') {
      suggestions.push({
        id: 'ip-protection',
        type: 'clause',
        title: 'Intellectual Property Protection',
        description: 'Add IP ownership and protection clauses',
        content: 'All intellectual property developed under this Agreement shall remain the property of the Company.',
        confidence: 88,
        priority: 'high',
        category: 'Intellectual Property',
        reasoning: 'IP protection is crucial in technology contracts to prevent disputes over ownership of innovations.',
        applicable: true
      });
    }

    // Jurisdiction-specific suggestions
    if (!data.standardClauses.includes('Governing Law')) {
      suggestions.push({
        id: 'governing-law',
        type: 'clause',
        title: 'Governing Law Clause',
        description: 'Specify which jurisdiction\'s laws will govern the contract',
        content: 'This Agreement shall be governed by and construed in accordance with the laws of [State], without regard to its conflict of laws principles.',
        confidence: 88,
        priority: 'medium',
        category: 'Legal Framework',
        reasoning: 'Clearly defined governing law prevents jurisdictional disputes and provides legal certainty.',
        applicable: true
      });
    }

    return suggestions;
  };

  const findMissingClauses = (): string[] => {
    const missing: string[] = [];
    
    if (!data.standardClauses.includes('Termination')) {
      missing.push('Termination Clause');
    }
    if (!data.standardClauses.includes('Confidentiality')) {
      missing.push('Confidentiality Agreement');
    }
    if (!data.standardClauses.includes('Force Majeure')) {
      missing.push('Force Majeure Clause');
    }
    
    return missing;
  };

  const identifyIssues = (): string[] => {
    const issues: string[] = [];
    
    if (!data.effectiveDate) {
      issues.push('Missing effective date may cause confusion about contract commencement');
    }
    if (data.parties.some(p => !p.address)) {
      issues.push('Incomplete party information may affect contract enforceability');
    }
    if (!data.description) {
      issues.push('Lack of clear contract description may lead to scope disputes');
    }
    
    return issues;
  };

  const generateRecommendations = (): string[] => {
    const recommendations: string[] = [];
    
    recommendations.push('Consider adding a dispute resolution clause to avoid costly litigation');
    recommendations.push('Include specific performance metrics and deliverables for clarity');
    recommendations.push('Add liability limitation clauses to protect against excessive damages');
    
    if (data.industry === 'technology') {
      recommendations.push('Include data protection and privacy clauses for GDPR compliance');
    }
    
    return recommendations;
  };

  const applySuggestion = (suggestion: AISuggestion) => {
    // Implementation would update the contract data with the suggestion
    console.log('Applying suggestion:', suggestion);
    // This would integrate with the contract wizard context to update the data
  };

  const dismissSuggestion = (suggestionId: string) => {
    if (analysis) {
      setAnalysis({
        ...analysis,
        suggestions: analysis.suggestions.filter(s => s.id !== suggestionId)
      });
    }
  };

  // Auto-analyze when contract data changes significantly
  useEffect(() => {
    if (data.contractType && data.jurisdiction) {
      const timer = setTimeout(() => {
        performAIAnalysis();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [data.contractType, data.jurisdiction, data.industry, performAIAnalysis]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-200 text-gray-800 border-gray-300';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-4">
      {/* AI Analysis Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">AI Contract Assistant</CardTitle>
            </div>
            <Button
              onClick={performAIAnalysis}
              disabled={isAnalyzing}
              size="sm"
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Analyze
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        
        {analysis && (
          <CardContent className="space-y-4">
            {/* Analysis Scores */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Shield className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">Risk Score</span>
                </div>
                <div className={`text-2xl font-bold ${getScoreColor(analysis.riskScore)}`}>
                  {analysis.riskScore}%
                </div>
              </div>
              <div className="text-center p-3 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium">Compliance</span>
                </div>
                <div className={`text-2xl font-bold ${getScoreColor(analysis.complianceScore)}`}>
                  {analysis.complianceScore}%
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="flex gap-4 text-sm text-muted-foreground">
              <span>{analysis.suggestions.length} suggestions</span>
              <span>{analysis.missingClauses.length} missing clauses</span>
              <span>{analysis.potentialIssues.length} potential issues</span>
            </div>
          </CardContent>
        )}
      </Card>

      {/* AI Suggestions */}
      {analysis && analysis.suggestions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Lightbulb className="h-4 w-4 text-yellow-600" />
              AI Suggestions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {analysis.suggestions.slice(0, 3).map((suggestion) => (
              <Alert key={suggestion.id} className="border-l-4">
                <div className="flex items-start gap-3">
                  <TrendingUp className="h-4 w-4 text-blue-600 mt-1" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{suggestion.title}</h4>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(suggestion.priority)}>
                          {suggestion.priority}
                        </Badge>
                        <Badge variant="outline">{suggestion.confidence}%</Badge>
                      </div>
                    </div>
                    <AlertDescription>{suggestion.description}</AlertDescription>
                    {suggestion.content && (
                      <div className="bg-muted/50 p-2 rounded text-sm">
                        {suggestion.content}
                      </div>
                    )}
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => applySuggestion(suggestion)}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Apply
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => dismissSuggestion(suggestion.id)}
                      >
                        Dismiss
                      </Button>
                    </div>
                  </div>
                </div>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Missing Clauses */}
      {analysis && analysis.missingClauses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              Missing Clauses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {analysis.missingClauses.map((clause, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">{clause}</span>
                  <Button size="sm" variant="outline">
                    <Plus className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Custom AI Query */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Brain className="h-4 w-4 text-purple-600" />
            Ask AI Assistant
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Textarea
            placeholder="Ask a specific question about your contract..."
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            rows={3}
          />
          <Button size="sm" disabled={!customPrompt.trim()}>
            <Brain className="h-3 w-3 mr-1" />
            Ask AI
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIContractAssistant;
