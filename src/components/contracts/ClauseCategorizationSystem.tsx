import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Book,
  Edit,
  Layers,
  Plus,
  Save,
  Search,
  Tag,
  Trash,
  X,
} from "lucide-react";

// Types
interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  parentId?: string;
}

interface Tag {
  id: string;
  name: string;
  color?: string;
}

interface ClauseCategorizationSystemProps {
  onCategoryCreate?: (category: Category) => void;
  onCategoryUpdate?: (category: Category) => void;
  onCategoryDelete?: (categoryId: string) => void;
  onTagCreate?: (tag: Tag) => void;
  onTagUpdate?: (tag: Tag) => void;
  onTagDelete?: (tagId: string) => void;
}

const ClauseCategorizationSystem: React.FC<ClauseCategorizationSystemProps> = ({
  onCategoryCreate,
  onCategoryUpdate,
  onCategoryDelete,
  onTagCreate,
  onTagUpdate,
  onTagDelete,
}) => {
  // State
  const [activeTab, setActiveTab] = useState<string>("categories");
  const [categories, setCategories] = useState<Category[]>([
    { id: "cat-1", name: "Liability", description: "Clauses related to liability and risk allocation", color: "#ef4444" },
    { id: "cat-2", name: "Confidentiality", description: "Clauses related to confidential information", color: "#3b82f6" },
    { id: "cat-3", name: "Termination", description: "Clauses related to contract termination", color: "#f97316" },
    { id: "cat-4", name: "Payment", description: "Clauses related to payment terms", color: "#22c55e" },
    { id: "cat-5", name: "Compliance", description: "Clauses related to regulatory compliance", color: "#eab308" },
    { id: "cat-6", name: "General", description: "General contract clauses", color: "#6b7280" },
    { id: "cat-7", name: "Intellectual Property", description: "Clauses related to IP rights", color: "#a855f7" },
    { id: "cat-8", name: "Indemnification", description: "Clauses related to indemnification", color: "#ec4899" },
    { id: "cat-9", name: "Force Majeure", description: "Clauses related to force majeure events", color: "#f97316" },
    { id: "cat-10", name: "Dispute Resolution", description: "Clauses related to dispute resolution", color: "#3b82f6" },
  ]);

  const [tags, setTags] = useState<Tag[]>([
    { id: "tag-1", name: "Standard", color: "#3b82f6" },
    { id: "tag-2", name: "Enhanced", color: "#a855f7" },
    { id: "tag-3", name: "High Risk", color: "#ef4444" },
    { id: "tag-4", name: "GDPR", color: "#3b82f6" },
    { id: "tag-5", name: "Financial", color: "#22c55e" },
    { id: "tag-6", name: "Regulatory", color: "#eab308" },
    { id: "tag-7", name: "Data Protection", color: "#3b82f6" },
    { id: "tag-8", name: "Risk Mitigation", color: "#f97316" },
    { id: "tag-9", name: "Sensitive", color: "#ec4899" },
    { id: "tag-10", name: "Extended", color: "#a855f7" },
  ]);

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isAddCategoryDialogOpen, setIsAddCategoryDialogOpen] = useState<boolean>(false);
  const [isAddTagDialogOpen, setIsAddTagDialogOpen] = useState<boolean>(false);
  const [isDeleteCategoryDialogOpen, setIsDeleteCategoryDialogOpen] = useState<boolean>(false);
  const [isDeleteTagDialogOpen, setIsDeleteTagDialogOpen] = useState<boolean>(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [selectedTagId, setSelectedTagId] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState<Omit<Category, "id">>({
    name: "",
    description: "",
    color: "#3b82f6", // Default to blue
  });
  const [newTag, setNewTag] = useState<Omit<Tag, "id">>({
    name: "",
    color: "#3b82f6", // Default to blue
  });

  // Filter categories and tags based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle category creation
  const handleCreateCategory = () => {
    if (!newCategory.name.trim()) return;

    const newCategoryWithId: Category = {
      id: `cat-${Date.now()}`,
      ...newCategory,
    };

    setCategories([...categories, newCategoryWithId]);
    if (onCategoryCreate) onCategoryCreate(newCategoryWithId);

    setNewCategory({
      name: "",
      description: "",
      color: "#3b82f6", // Default to blue
    });

    setIsAddCategoryDialogOpen(false);
  };

  // Handle tag creation
  const handleCreateTag = () => {
    if (!newTag.name.trim()) return;

    const newTagWithId: Tag = {
      id: `tag-${Date.now()}`,
      ...newTag,
    };

    setTags([...tags, newTagWithId]);
    if (onTagCreate) onTagCreate(newTagWithId);

    setNewTag({
      name: "",
      color: "#3b82f6", // Default to blue
    });

    setIsAddTagDialogOpen(false);
  };

  // Handle category deletion
  const handleDeleteCategory = () => {
    if (!selectedCategoryId) return;

    setCategories(categories.filter(category => category.id !== selectedCategoryId));
    if (onCategoryDelete) onCategoryDelete(selectedCategoryId);

    setSelectedCategoryId(null);
    setIsDeleteCategoryDialogOpen(false);
  };

  // Handle tag deletion
  const handleDeleteTag = () => {
    if (!selectedTagId) return;

    setTags(tags.filter(tag => tag.id !== selectedTagId));
    if (onTagDelete) onTagDelete(selectedTagId);

    setSelectedTagId(null);
    setIsDeleteTagDialogOpen(false);
  };

  // Render color badge using design system colors
  const renderColorBadge = (color: string) => {
    // Map color hex values to our design system color classes
    const colorClassMap: Record<string, string> = {
      "#6b7280": "bg-gray-200 border-gray-300", // gray
      "#ef4444": "bg-red-50 border-red-200",    // red
      "#22c55e": "bg-green-50 border-green-200", // green
      "#3b82f6": "bg-blue-50 border-blue-200",  // blue
      "#eab308": "bg-yellow-50 border-yellow-200", // yellow
      "#a855f7": "bg-purple-50 border-purple-200", // purple
      "#ec4899": "bg-pink-50 border-pink-200",  // pink
      "#f97316": "bg-orange-50 border-orange-200", // orange
    };

    return (
      <div
        className={`w-3 h-3 rounded-full border ${colorClassMap[color] || "bg-gray-200 border-gray-300"}`}
      />
    );
  };

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-medium">Clause Organization System</h2>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8 h-9 w-[200px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="categories">
            <Layers className="h-4 w-4 mr-2" />
            Categories
          </TabsTrigger>
          <TabsTrigger value="tags">
            <Tag className="h-4 w-4 mr-2" />
            Tags
          </TabsTrigger>
        </TabsList>

        <TabsContent value="categories" className="mt-4">
          <Card>
            <CardHeader className="pb-3 flex flex-row items-center justify-between">
              <div>
                <CardTitle>Categories</CardTitle>
                <CardDescription>Organize clauses into categories</CardDescription>
              </div>
              <Button
                size="sm"
                onClick={() => setIsAddCategoryDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-2">
                  {filteredCategories.length > 0 ? (
                    filteredCategories.map((category) => (
                      <div
                        key={category.id}
                        className="flex items-center justify-between p-3 border border-gray-300 rounded-md hover:bg-gray-100"
                      >
                        <div className="flex items-center gap-3">
                          {renderColorBadge(category.color || "#3b82f6")}
                          <div>
                            <div className="font-medium">{category.name}</div>
                            {category.description && (
                              <div className="text-xs text-muted-foreground">{category.description}</div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              setNewCategory({
                                name: category.name,
                                description: category.description,
                                color: category.color,
                                parentId: category.parentId,
                              });
                              setSelectedCategoryId(category.id);
                              setIsAddCategoryDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive"
                            onClick={() => {
                              setSelectedCategoryId(category.id);
                              setIsDeleteCategoryDialogOpen(true);
                            }}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Book className="h-12 w-12 text-muted-foreground mb-3" />
                      <h3 className="text-lg font-medium mb-1">No categories found</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {searchTerm ? "Try a different search term" : "Create your first category"}
                      </p>
                      {!searchTerm && (
                        <Button
                          variant="outline"
                          onClick={() => setIsAddCategoryDialogOpen(true)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Category
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tags" className="mt-4">
          <Card>
            <CardHeader className="pb-3 flex flex-row items-center justify-between">
              <div>
                <CardTitle>Tags</CardTitle>
                <CardDescription>Create and manage tags for clauses</CardDescription>
              </div>
              <Button
                size="sm"
                onClick={() => setIsAddTagDialogOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Tag
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] pr-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  {filteredTags.map((tag) => (
                    <div
                      key={tag.id}
                      className="flex items-center gap-2 border border-gray-300 rounded-md px-3 py-1 hover:bg-gray-100"
                    >
                      {renderColorBadge(tag.color || "#3b82f6")}
                      <span className="text-sm">{tag.name}</span>
                      <div className="flex items-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => {
                            setNewTag({
                              name: tag.name,
                              color: tag.color,
                            });
                            setSelectedTagId(tag.id);
                            setIsAddTagDialogOpen(true);
                          }}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-destructive"
                          onClick={() => {
                            setSelectedTagId(tag.id);
                            setIsDeleteTagDialogOpen(true);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  {filteredTags.length === 0 && (
                    <div className="flex flex-col items-center justify-center py-8 text-center w-full">
                      <Tag className="h-12 w-12 text-muted-foreground mb-3" />
                      <h3 className="text-lg font-medium mb-1">No tags found</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {searchTerm ? "Try a different search term" : "Create your first tag"}
                      </p>
                      {!searchTerm && (
                        <Button
                          variant="outline"
                          onClick={() => setIsAddTagDialogOpen(true)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Tag
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add/Edit Category Dialog */}
      <Dialog open={isAddCategoryDialogOpen} onOpenChange={setIsAddCategoryDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedCategoryId ? "Edit Category" : "Add New Category"}</DialogTitle>
            <DialogDescription>
              {selectedCategoryId ? "Update the category details" : "Create a new category for organizing clauses"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Name</label>
              <Input
                placeholder="Category name"
                value={newCategory.name}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Description (optional)</label>
              <Input
                placeholder="Brief description"
                value={newCategory.description || ""}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Color</label>
              <div className="flex flex-wrap gap-2">
                {["#ef4444", "#22c55e", "#3b82f6", "#eab308", "#a855f7", "#ec4899", "#f97316", "#6b7280"].map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={`w-6 h-6 rounded-full border ${newCategory.color === color ? 'ring-2 ring-offset-2 ring-gray-400' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setNewCategory({ ...newCategory, color })}
                  />
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddCategoryDialogOpen(false);
              setSelectedCategoryId(null);
              setNewCategory({
                name: "",
                description: "",
                color: "#3b82f6", // Default to blue
              });
            }}>
              Cancel
            </Button>
            <Button onClick={() => {
              if (selectedCategoryId) {
                // Update existing category
                const updatedCategory = {
                  id: selectedCategoryId,
                  ...newCategory,
                };
                setCategories(categories.map(cat => cat.id === selectedCategoryId ? updatedCategory : cat));
                if (onCategoryUpdate) onCategoryUpdate(updatedCategory);
                setSelectedCategoryId(null);
              } else {
                // Create new category
                handleCreateCategory();
              }
            }}>
              <Save className="h-4 w-4 mr-2" />
              {selectedCategoryId ? "Update" : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add/Edit Tag Dialog */}
      <Dialog open={isAddTagDialogOpen} onOpenChange={setIsAddTagDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedTagId ? "Edit Tag" : "Add New Tag"}</DialogTitle>
            <DialogDescription>
              {selectedTagId ? "Update the tag details" : "Create a new tag for labeling clauses"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Name</label>
              <Input
                placeholder="Tag name"
                value={newTag.name}
                onChange={(e) => setNewTag({ ...newTag, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Color</label>
              <div className="flex flex-wrap gap-2">
                {["#ef4444", "#22c55e", "#3b82f6", "#eab308", "#a855f7", "#ec4899", "#f97316", "#6b7280"].map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={`w-6 h-6 rounded-full border ${newTag.color === color ? 'ring-2 ring-offset-2 ring-gray-400' : 'border-gray-200'}`}
                    style={{ backgroundColor: color }}
                    onClick={() => setNewTag({ ...newTag, color })}
                  />
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsAddTagDialogOpen(false);
              setSelectedTagId(null);
              setNewTag({
                name: "",
                color: "#3b82f6", // Default to blue
              });
            }}>
              Cancel
            </Button>
            <Button onClick={() => {
              if (selectedTagId) {
                // Update existing tag
                const updatedTag = {
                  id: selectedTagId,
                  ...newTag,
                };
                setTags(tags.map(tag => tag.id === selectedTagId ? updatedTag : tag));
                if (onTagUpdate) onTagUpdate(updatedTag);
                setSelectedTagId(null);
              } else {
                // Create new tag
                handleCreateTag();
              }
            }}>
              <Save className="h-4 w-4 mr-2" />
              {selectedTagId ? "Update" : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Category Confirmation Dialog */}
      <AlertDialog open={isDeleteCategoryDialogOpen} onOpenChange={setIsDeleteCategoryDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this category? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsDeleteCategoryDialogOpen(false);
              setSelectedCategoryId(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteCategory} className="bg-destructive text-destructive-foreground">
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Tag Confirmation Dialog */}
      <AlertDialog open={isDeleteTagDialogOpen} onOpenChange={setIsDeleteTagDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tag</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this tag? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setIsDeleteTagDialogOpen(false);
              setSelectedTagId(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteTag} className="bg-destructive text-destructive-foreground">
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ClauseCategorizationSystem;
