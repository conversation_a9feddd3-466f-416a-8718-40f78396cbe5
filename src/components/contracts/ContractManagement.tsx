import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import {
  Plus,
  Filter as FilterIcon,
  LayoutGrid,
  LayoutList,
  Bookmark,
  Download,
  Share2,
  Trash2,
  CheckCircle2,
  AlertCircle,
  Clock,
  Archive,
  Tag,
  ChevronDown,
  FileEdit,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import UnifiedContractView from "./UnifiedContractView";
import ContractDetails from "./ContractDetails";
import UnifiedContractPreviewModal from "../modals/UnifiedContractPreviewModal";
import UnifiedExportManager from "../exports/UnifiedExportManager";
import { useApi } from "@/lib/api";
import { ContractService } from "@/services/api-services";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import SearchFilterBar from "@/components/ui/search-filter-bar";
import { useSearchAndFilter } from "@/hooks/useSearchAndFilter";
import { FilterGroup } from "@/components/ui/enhanced-filter-panel";
import type { Contract } from "@/services/api-types";
import { ProgressiveLoader } from "@/components/ui/progressive-loader";
import { LoadingBar } from "@/components/ui/loading-indicator";
// import EnhancedTable, { type ColumnDef } from "@/components/ui/enhanced-table";
// import { useTableState } from "@/hooks/useTableState";


interface ContractManagementProps {
  initialTab?: string;
}

type ContractStatus = "active" | "draft" | "pending_approval" | "expired" | "all";

const ContractManagement = ({
  initialTab = "all",
}: ContractManagementProps) => {
  const [activeTab, setActiveTab] = useState<ContractStatus>(initialTab as ContractStatus);
  const [selectedContract, setSelectedContract] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");

  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewContractId, setPreviewContractId] = useState<string | null>(null);

  // API and data state
  const { currentWorkspace } = useClerkWorkspace();
  const { fetch } = useApi();
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Enhanced search and filter state
  const searchAndFilter = useSearchAndFilter({
    data: contracts,
    searchFields: ["title", "counterparty", "type"],
    filterFunctions: {
      status: (contract: Contract, value: string) =>
        value === "all" || contract.status === value,
      dateRange: (contract: Contract, value: { from?: Date; to?: Date }) => {
        if (!value.from && !value.to) return true;
        const contractDate = new Date(contract.created_at);
        const fromMatch = !value.from || contractDate >= value.from;
        const toMatch = !value.to || contractDate <= value.to;
        return fromMatch && toMatch;
      },
      value: (contract: Contract, value: string) => {
        if (!value) return true;
        return contract.value?.toLowerCase().includes(value.toLowerCase()) || false;
      },
    },
    defaultFilters: {
      status: activeTab === "all" ? "" : activeTab,
    },
  });

  // Filter groups for enhanced filtering
  const filterGroups: FilterGroup[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "", label: "All Statuses" },
        { value: "active", label: "Active" },
        { value: "draft", label: "Draft" },
        { value: "pending_approval", label: "Pending Approval" },
        { value: "expired", label: "Expired" },
        { value: "rejected", label: "Rejected" },
        { value: "terminated", label: "Terminated" },
      ],
      defaultOpen: true,
    },
    {
      key: "dateRange",
      label: "Date Range",
      type: "date-range",
      placeholder: "Select date range",
    },
    {
      key: "value",
      label: "Contract Value",
      type: "text",
      placeholder: "Search by value...",
    },
  ];
  const [loadingStartTime, setLoadingStartTime] = useState<number>(Date.now());

  // Enhanced table state management (temporarily commented out)
  // const tableState = useTableState({
  //   data: searchAndFilter.filteredData,
  //   getRowId: (contract: Contract) => contract.id,
  //   defaultSort: { key: "created_at", direction: "desc" },
  // });

  // Temporary mock for tableState
  const tableState = {
    selectedRows: [],
    handleRowSelect: (_contractId: string, _isChecked: boolean) => {},
    handleSelectAll: (_isChecked: boolean) => {},
    deselectAll: () => {},
    sortedData: searchAndFilter.filteredData,
    sortConfig: { key: "", direction: null },
    handleSort: () => {},
  };

  // Column definitions for enhanced table (temporarily commented out)
  /*
  const columns: ColumnDef<Contract>[] = [
    {
      key: "title",
      label: "Contract",
      sortable: true,
      render: (_: any, contract: Contract) => (
        <div>
          <div
            className="font-medium cursor-pointer hover:underline transition-colors"
            onClick={() => handleContractSelect(contract.id)}
          >
            {contract.title}
          </div>
          {contract.value && (
            <div className="text-sm text-muted-foreground">
              Value: {contract.value} {contract.currency}
            </div>
          )}
        </div>
      ),
    },
    {
      key: "type",
      label: "Type",
      sortable: true,
    },
    {
      key: "counterparty",
      label: "Counterparty",
      sortable: true,
      render: (value: any) => value || "-",
    },
    {
      key: "created_at",
      label: "Created",
      sortable: true,
      render: (value: any) => new Date(value).toLocaleDateString(),
    },
    {
      key: "expiry_date",
      label: "Expiry",
      sortable: true,
      render: (value: any) => value ? new Date(value).toLocaleDateString() : "-",
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (value: any) => renderStatusBadge(value),
    },
    {
      key: "actions",
      label: "Actions",
      sortable: false,
      width: "120px",
      render: (_: any, contract: Contract) => (
        <div className="flex items-center gap-2 table-row-actions">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              handlePreviewContract(contract.id);
            }}
            title="Quick Preview"
            className="h-8 w-8 hover:bg-muted"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <DocumentExportButton
            contractId={contract.id}
            contractTitle={contract.title}
            variant="ghost"
            size="sm"
            showLabel={false}
            enableTemplateSelection={true}
            workspaceId={currentWorkspace?.id}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleContractSelect(contract.id)}>
                <ExternalLink className="mr-2 h-4 w-4" /> Open
              </DropdownMenuItem>
              <DropdownMenuItem>
                <FileEdit className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" /> Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Mail className="mr-2 h-4 w-4" /> Email
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];
  */

  // Memoize workspace ID to prevent unnecessary re-renders
  const workspaceId = useMemo(() => currentWorkspace?.id, [currentWorkspace?.id]);

  // Fetch contracts when workspace or tab changes
  useEffect(() => {
    // Create an AbortController for this effect
    const abortController = new AbortController();

    const fetchContracts = async () => {
      if (!workspaceId) {
        setContracts([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setLoadingStartTime(Date.now());

      try {
        // Check if the request was cancelled before starting
        if (abortController.signal.aborted) {
          return;
        }

        // Prepare filter parameters
        const params: {
          workspace_id: string;
          status?: string;
          search?: string;
        } = {
          workspace_id: workspaceId
        };

        // Add status filter if not "all"
        if (activeTab !== "all") {
          params.status = activeTab;
        }

        // Add search query if present
        if (searchAndFilter.state.search.global) {
          params.search = searchAndFilter.state.search.global;
        }

        const result = await fetch(
          () => ContractService.getContracts(params),
          "Loading contracts...",
          "Failed to load contracts"
        );

        // Check if the request was cancelled before setting state
        if (!abortController.signal.aborted && result) {
          setContracts(result);
        }
      } catch (err) {
        // Only handle errors if the request wasn't cancelled
        if (!abortController.signal.aborted) {
          console.error("Error fetching contracts:", err);
          setError("Failed to load contracts. Please try again later.");
        }
      } finally {
        // Only update loading state if the request wasn't cancelled
        if (!abortController.signal.aborted) {
          // Ensure minimum loading time for smooth UX (prevent flashing)
          const minLoadingTime = 800; // 800ms minimum
          const elapsedTime = Date.now() - loadingStartTime;
          const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

          setTimeout(() => {
            setLoading(false);
          }, remainingTime);
        }
      }
    };

    fetchContracts();

    // Cleanup function to abort the request if the component unmounts or dependencies change
    return () => {
      abortController.abort();
    };
  }, [workspaceId, activeTab, searchAndFilter.state.search.global, fetch]);

  const handleContractSelect = (contractId: string) => {
    setSelectedContract(contractId);
  };

  const handleCreateContract = () => {
    // Navigate to the contract creation method page
    window.location.href = "/app/contracts/create";
  };

  const handleBackToList = () => {
    setSelectedContract(null);
  };

  const handleSelectContract = (contractId: string, isChecked: boolean) => {
    tableState.handleRowSelect(contractId, isChecked);
  };

  const handleSelectAll = (isChecked: boolean) => {
    tableState.handleSelectAll(isChecked);
  };

  const handlePreviewContract = (contractId: string) => {
    setPreviewContractId(contractId);
    setIsPreviewOpen(true);
  };

  // Bulk action handlers
  const handleBulkDownload = () => {
    toast({
      title: "Downloading contracts",
      description: `${tableState.selectedRows.length} contracts are being prepared for download.`,
    });
    // Implementation would connect to backend API to generate and download files
  };

  const handleBulkShare = () => {
    toast({
      title: "Share contracts",
      description: `Preparing to share ${tableState.selectedRows.length} contracts.`,
    });
    // Implementation would open a sharing dialog
  };

  const handleBulkDelete = () => {
    toast({
      title: "Contracts deleted",
      description: `${tableState.selectedRows.length} contracts have been deleted.`,
      variant: "destructive",
    });
    // Implementation would connect to backend API to delete contracts
    tableState.deselectAll();
  };

  const handleBulkChangeStatus = (status: ContractStatus) => {
    if (status === "all") return;

    toast({
      title: "Status updated",
      description: `${tableState.selectedRows.length} contracts updated to "${status}" status.`,
    });
    // Implementation would connect to backend API to update contract status
  };



  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-200 text-green-800 hover:bg-green-200">Active</Badge>;
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "pending_approval":
        return <Badge className="bg-blue-200 text-blue-800 hover:bg-blue-200">Pending Approval</Badge>;
      case "expired":
        return <Badge className="bg-red-200 text-red-800 hover:bg-red-200">Expired</Badge>;
      case "terminated":
        return <Badge className="bg-orange-200 text-orange-800 hover:bg-orange-200">Terminated</Badge>;
      case "rejected":
        return <Badge className="bg-red-200 text-red-800 hover:bg-red-200">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <ProgressiveLoader isLoading={loading} viewMode={viewMode}>
      {/* Loading bar for data fetching */}
      <LoadingBar isLoading={loading} />

      <div className="page-container">
        {/* Contract Preview Modal */}
        <UnifiedContractPreviewModal
          open={isPreviewOpen}
          onOpenChange={setIsPreviewOpen}
          contractId={previewContractId}
          showActions={true}
          onEdit={handleContractSelect}
          onOpen={handleContractSelect}
        />

        <div className="page-header justify-end">
          <Button size="sm" onClick={handleCreateContract}>
            <Plus className="mr-1.5 h-4 w-4" />
            New Contract
          </Button>
        </div>

      {!selectedContract ? (
        <>
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as ContractStatus)}
            className="w-full"
          >
            <TabsList className="grid grid-cols-5 w-full max-w-3xl mb-5">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="draft">Drafts</TabsTrigger>
              <TabsTrigger value="pending_approval">Pending</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="expired">Expired</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-0">
              <div className="flex flex-col md:flex-row spacing-tight mb-5 items-center">
                {tableState.selectedRows.length > 0 ? (
                  <div className="flex items-center gap-2 flex-1">
                    <span className="text-xs font-medium">
                      {tableState.selectedRows.length} {tableState.selectedRows.length === 1 ? 'contract' : 'contracts'} selected
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="ml-2 h-8">
                          Bulk Actions <ChevronDown className="ml-2 h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="start" className="w-56">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={handleBulkDownload}>
                          <Download className="mr-2 h-4 w-4" /> Download
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleBulkShare}>
                          <Share2 className="mr-2 h-4 w-4" /> Share
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuLabel className="text-xs">Export Options</DropdownMenuLabel>
                        <DropdownMenuSub>
                          <DropdownMenuSubTrigger>
                            <Tag className="mr-2 h-4 w-4" /> Change Status
                          </DropdownMenuSubTrigger>
                          <DropdownMenuSubContent>
                            <DropdownMenuItem onClick={() => handleBulkChangeStatus("draft")}>
                              <Clock className="mr-2 h-4 w-4" /> Draft
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleBulkChangeStatus("pending_approval")}>
                              <AlertCircle className="mr-2 h-4 w-4" /> Pending Approval
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleBulkChangeStatus("active")}>
                              <CheckCircle2 className="mr-2 h-4 w-4" /> Active
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleBulkChangeStatus("expired")}>
                              <Archive className="mr-2 h-4 w-4" /> Expired
                            </DropdownMenuItem>
                          </DropdownMenuSubContent>
                        </DropdownMenuSub>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={handleBulkDelete} className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <UnifiedExportManager
                      mode="batch"
                      contractIds={tableState.selectedRows}
                      contractTitles={contracts
                        .filter(c => tableState.selectedRows.includes(c.id))
                        .map(c => c.title)
                      }
                      variant="outline"
                      size="sm"
                      className="h-8"
                      showHistory={true}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => tableState.deselectAll()}
                      className="ml-auto h-7 text-xs"
                    >
                      Clear selection
                    </Button>
                  </div>
                ) : (
                  <div className="flex-1">
                    <SearchFilterBar
                      searchValue={searchAndFilter.state.search}
                      onSearchChange={searchAndFilter.actions.setSearch}
                      searchPlaceholder="Search contracts by title, counterparty, content..."
                      recentSearches={searchAndFilter.state.recentSearches}
                      suggestions={searchAndFilter.getSearchSuggestions()}
                      filterValues={searchAndFilter.state.filters}
                      onFilterChange={searchAndFilter.actions.setFilters}
                      filterGroups={filterGroups}
                      savedFilters={searchAndFilter.state.savedFilters}
                      onSaveFilter={searchAndFilter.actions.saveFilter}
                      onLoadFilter={searchAndFilter.actions.loadFilter}
                      totalResults={searchAndFilter.filteredData.length}
                      compact={true}
                      showFilterChips={true}
                    />
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <div className="flex border rounded-md">
                    <Button
                      variant={viewMode === "list" ? "secondary" : "ghost"}
                      size="icon"
                      className="rounded-r-none h-8 w-8"
                      onClick={() => setViewMode("list")}
                    >
                      <LayoutList className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "grid" ? "secondary" : "ghost"}
                      size="icon"
                      className="rounded-l-none h-8 w-8"
                      onClick={() => setViewMode("grid")}
                    >
                      <LayoutGrid className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button variant="outline" size="sm" className="flex items-center gap-2 h-8">
                    <Bookmark className="h-3 w-3" />
                    <span className="text-xs">Saved</span>
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-2 h-8">
                    <FilterIcon className="h-3 w-3" />
                    <span className="text-xs">Filter</span>
                  </Button>
                </div>
              </div>

              {viewMode === "list" ? (
                error ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <p className="text-destructive mb-2">Error: {error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.reload()}
                    >
                      Try Again
                    </Button>
                  </div>
                ) : contracts.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <FileEdit className="h-12 w-12 text-muted-foreground mb-3" />
                    <h3 className="text-lg font-medium mb-1">No contracts found</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {searchAndFilter.hasActiveSearch || searchAndFilter.hasActiveFilters ? "Try adjusting your search or filters" : "Create your first contract"}
                    </p>
                    {!searchAndFilter.hasActiveSearch && !searchAndFilter.hasActiveFilters && (
                      <Button onClick={handleCreateContract}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Contract
                      </Button>
                    )}
                  </div>
                ) : (
                  <UnifiedContractView
                    contracts={searchAndFilter.filteredData}
                    viewMode="list"
                    onContractSelect={handleContractSelect}
                    onPreviewContract={handlePreviewContract}
                    onSelectContract={handleSelectContract}
                    onSelectAll={handleSelectAll}
                    renderStatusBadge={renderStatusBadge}
                    showSelection={true}
                    emptyMessage="No contracts found"
                  />
                )
              ) : (
                error ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <p className="text-destructive mb-2">Error: {error}</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.reload()}
                    >
                      Try Again
                    </Button>
                  </div>
                ) : contracts.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <FileEdit className="h-12 w-12 text-muted-foreground mb-3" />
                    <h3 className="text-lg font-medium mb-1">No contracts found</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      {searchAndFilter.hasActiveSearch || searchAndFilter.hasActiveFilters ? "Try adjusting your search or filters" : "Create your first contract"}
                    </p>
                    {!searchAndFilter.hasActiveSearch && !searchAndFilter.hasActiveFilters && (
                      <Button onClick={handleCreateContract}>
                        <Plus className="h-4 w-4 mr-2" />
                        New Contract
                      </Button>
                    )}
                  </div>
                ) : (
                  <UnifiedContractView
                    contracts={searchAndFilter.filteredData}
                    viewMode="grid"
                    onContractSelect={handleContractSelect}
                    onPreviewContract={handlePreviewContract}
                    onSelectContract={handleSelectContract}
                    onSelectAll={handleSelectAll}
                    renderStatusBadge={renderStatusBadge}
                    showSelection={true}
                    emptyMessage="No contracts found"
                  />
                )
              )}
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <ContractDetails
          contractId={selectedContract as string}
          onBack={handleBackToList}
        />
      )}
      </div>


    </ProgressiveLoader>
  );
};

export default ContractManagement;