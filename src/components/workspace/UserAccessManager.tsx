import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Search } from "@/components/ui/search";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,

  Trash,
  UserPlus,
  Plus,
  Shield,
  Users,
  Edit,
  MoreHorizontal,
  User,
  UserCog,
  Lock,
  Loader2
} from "lucide-react";
import { useApi } from "@/lib/api";
import { RoleService, PermissionService, WorkspaceMemberService, WorkspaceService } from "@/services/api-services";
import type { Role as ApiRole, Permission as ApiPermission, WorkspaceMember } from "@/services/api-types";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

interface Workspace {
  id: string;
  name: string;
  description: string;
  members: number;
  contracts?: number;
  createdBy?: string;
  createdDate?: string;
  isActive?: boolean;
}

interface User {
  id: string;
  name: string;
  email: string;
  roleId: string;
  dateAdded: string;
  status?: "active" | "inactive" | "pending";
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isSystem?: boolean;
  userCount: number;
}

interface UserAccessManagerProps {
  workspace?: Workspace;
  onClose: () => void;
  onUsersUpdated?: () => void;
}

const UserAccessManager = ({
  workspace,
  onClose,
  onUsersUpdated,
}: UserAccessManagerProps) => {
  const { fetch } = useApi();
  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState<string>("users");
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserRoleId, setNewUserRoleId] = useState("");
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState<boolean>(false);
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState<boolean>(false);
  const [selectedRoleId, setSelectedRoleId] = useState<string | null>(null);
  const [newRole, setNewRole] = useState<Omit<Role, "id" | "userCount">>({
    name: "",
    description: "",
    permissions: [],
  });

  // State for API data
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch roles, permissions, and users from API
  useEffect(() => {
    const fetchData = async () => {
      if (!workspace) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch permissions
        const permissionsResult = await fetch(
          () => PermissionService.getWorkspacePermissions(workspace.id),
          "Loading permissions...",
          "Failed to load permissions"
        );

        if (permissionsResult) {
          // Map API permissions to UI permissions
          const mappedPermissions: Permission[] = permissionsResult.map((permission: ApiPermission) => ({
            id: permission.id,
            name: permission.name,
            description: permission.description,
            category: permission.category
          }));

          setPermissions(mappedPermissions);
        }

        // Fetch roles
        const rolesResult = await fetch(
          () => RoleService.getWorkspaceRoles(workspace.id),
          "Loading roles...",
          "Failed to load roles"
        );

        if (rolesResult) {
          // Map API roles to UI roles
          const mappedRoles: Role[] = rolesResult.map((role: ApiRole) => ({
            id: role.id,
            name: role.name,
            description: role.description,
            permissions: role.permissions,
            isSystem: role.is_system,
            userCount: role.user_count || 0
          }));

          setRoles(mappedRoles);

          // Set default role for new users
          if (mappedRoles.length > 0 && !newUserRoleId) {
            // Find a non-admin role to use as default
            const defaultRole = mappedRoles.find(r => !r.isSystem) || mappedRoles[0];
            setNewUserRoleId(defaultRole.id);
          }
        }

        // Fetch workspace members
        const membersResult = await fetch(
          () => WorkspaceMemberService.getWorkspaceMembers(workspace.id),
          "Loading users...",
          "Failed to load users"
        );

        if (membersResult) {
          // Map API members to UI users
          const mappedUsers: User[] = membersResult.map((member: WorkspaceMember) => ({
            id: member.user_id,
            name: member.user.name,
            email: member.user.email,
            roleId: member.role_id,
            dateAdded: member.joined_at,
            status: member.status
          }));

          setUsers(mappedUsers);
        }
      } catch (err) {
        console.error("Error fetching workspace data:", err);
        setError("Failed to load workspace data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [workspace, fetch, newUserRoleId]);

  const handleAddUser = () => {
    setIsAddingUser(true);
  };

  const handleSubmitNewUser = async () => {
    if (!workspace || !newUserEmail || !newUserRoleId) return;

    try {
      // Invite user to workspace
      const result = await fetch(
        () => WorkspaceService.inviteUserToWorkspace(workspace.id, newUserEmail, newUserRoleId),
        "Inviting user...",
        "Failed to invite user"
      );

      if (result) {
        toast({
          title: "User invited",
          description: `An invitation has been sent to ${newUserEmail}`,
        });

        // Add the user to the local state with pending status
        const newUser: User = {
          id: `temp-${Date.now()}`, // Temporary ID until user accepts invitation
          name: newUserEmail.split('@')[0], // Temporary name
          email: newUserEmail,
          roleId: newUserRoleId,
          dateAdded: new Date().toISOString(),
          status: "pending"
        };

        setUsers([...users, newUser]);

        // Update role user count
        setRoles(roles.map(role =>
          role.id === newUserRoleId
            ? { ...role, userCount: role.userCount + 1 }
            : role
        ));

        // Refresh the user list
        if (onUsersUpdated) {
          onUsersUpdated();
        }
      }
    } catch (error) {
      console.error("Error inviting user:", error);
      toast({
        title: "Error",
        description: "Failed to invite user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAddingUser(false);
      setNewUserEmail("");
      setNewUserRoleId(roles.length > 0 ? roles[0].id : "");
    }
  };

  const handleChangeUserRole = async (userId: string, newRoleId: string) => {
    if (!workspace) return;

    // Find the user and their current role
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const oldRoleId = user.roleId;

    try {
      // Update user's role in the workspace
      const result = await fetch(
        () => WorkspaceMemberService.updateMemberRole(workspace.id, userId, newRoleId),
        "Updating role...",
        "Failed to update role"
      );

      if (result) {
        toast({
          title: "Role updated",
          description: "The user's role has been updated successfully.",
        });

        // Update the user's role in local state
        setUsers(users.map(u =>
          u.id === userId ? { ...u, roleId: newRoleId } : u
        ));

        // Update role user counts
        setRoles(roles.map(role => {
          if (role.id === oldRoleId) {
            return { ...role, userCount: Math.max(0, role.userCount - 1) };
          } else if (role.id === newRoleId) {
            return { ...role, userCount: role.userCount + 1 };
          }
          return role;
        }));

        // Refresh the user list
        if (onUsersUpdated) {
          onUsersUpdated();
        }
      }
    } catch (error) {
      console.error("Error updating user role:", error);
      toast({
        title: "Error",
        description: "Failed to update user role. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveUser = async (userId: string) => {
    if (!workspace) return;

    // Find the user and their role
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const roleId = user.roleId;

    try {
      // Remove user from workspace
      const result = await fetch(
        () => WorkspaceMemberService.removeMember(workspace.id, userId),
        "Removing user...",
        "Failed to remove user"
      );

      if (result) {
        toast({
          title: "User removed",
          description: "The user has been removed from the workspace.",
        });

        // Remove the user from local state
        setUsers(users.filter(u => u.id !== userId));

        // Update role user count
        setRoles(roles.map(role =>
          role.id === roleId
            ? { ...role, userCount: Math.max(0, role.userCount - 1) }
            : role
        ));

        // Refresh the user list
        if (onUsersUpdated) {
          onUsersUpdated();
        }
      }
    } catch (error) {
      console.error("Error removing user:", error);
      toast({
        title: "Error",
        description: "Failed to remove user. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleToggleUserStatus = async (userId: string) => {
    if (!workspace) return;

    // Find the user
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Toggle between active and inactive
    const newStatus = user.status === "active" ? "inactive" : "active";

    try {
      // Update user status in workspace
      const result = await fetch(
        () => WorkspaceMemberService.updateMemberStatus(workspace.id, userId, newStatus),
        "Updating status...",
        "Failed to update status"
      );

      if (result) {
        toast({
          title: "Status updated",
          description: `The user is now ${newStatus}.`,
        });

        // Update the user's status in local state
        setUsers(users.map(u =>
          u.id === userId ? { ...u, status: newStatus } : u
        ));

        // Refresh the user list
        if (onUsersUpdated) {
          onUsersUpdated();
        }
      }
    } catch (error) {
      console.error("Error updating user status:", error);
      toast({
        title: "Error",
        description: "Failed to update user status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Role management functions
  const handleCreateRole = async () => {
    if (!workspace || !newRole.name.trim()) return;

    try {
      // Create role in the workspace
      const result = await fetch(
        () => RoleService.createRole({
          name: newRole.name,
          description: newRole.description,
          permissions: newRole.permissions,
          workspace_id: workspace.id
        }),
        "Creating role...",
        "Failed to create role"
      );

      if (result) {
        toast({
          title: "Role created",
          description: "The new role has been created successfully.",
        });

        // Add the new role to local state
        const newRoleWithId: Role = {
          id: result.id,
          name: result.name,
          description: result.description,
          permissions: result.permissions,
          userCount: 0,
          isSystem: false
        };

        setRoles([...roles, newRoleWithId]);

        // Reset form
        setNewRole({
          name: "",
          description: "",
          permissions: [],
        });
      }
    } catch (error) {
      console.error("Error creating role:", error);
      toast({
        title: "Error",
        description: "Failed to create role. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAddRoleDialogOpen(false);
    }
  };

  const handleDeleteRole = async () => {
    if (!workspace || !selectedRoleId) return;

    // Check if role has users
    const roleToDelete = roles.find(role => role.id === selectedRoleId);
    if (!roleToDelete) return;

    if (roleToDelete.userCount > 0) {
      toast({
        title: "Cannot delete role",
        description: `This role has ${roleToDelete.userCount} users assigned. Please reassign these users first.`,
        variant: "destructive",
      });
      setIsDeleteRoleDialogOpen(false);
      return;
    }

    if (roleToDelete.isSystem) {
      toast({
        title: "Cannot delete role",
        description: "System roles cannot be deleted.",
        variant: "destructive",
      });
      setIsDeleteRoleDialogOpen(false);
      return;
    }

    try {
      // Delete role from workspace
      const result = await fetch(
        () => RoleService.deleteRole(workspace.id, selectedRoleId),
        "Deleting role...",
        "Failed to delete role"
      );

      if (result) {
        toast({
          title: "Role deleted",
          description: "The role has been deleted successfully.",
        });

        // Remove the role from local state
        setRoles(roles.filter(role => role.id !== selectedRoleId));
      }
    } catch (error) {
      console.error("Error deleting role:", error);
      toast({
        title: "Error",
        description: "Failed to delete role. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSelectedRoleId(null);
      setIsDeleteRoleDialogOpen(false);
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    setNewRole(prev => {
      const permissions = prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId];

      return { ...prev, permissions };
    });
  };

  const getRoleBadge = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (!role) return null;

    switch (role.id) {
      case "role-1": // Admin
        return (
          <Badge variant="default" className="capitalize">
            {role.name}
          </Badge>
        );
      case "role-2": // Contract Manager
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            {role.name}
          </Badge>
        );
      case "role-3": // Legal Reviewer
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            {role.name}
          </Badge>
        );
      case "role-4": // Read Only
        return (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-700 border-amber-200"
          >
            {role.name}
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {role.name}
          </Badge>
        );
    }
  };

  const getStatusBadge = (status: User["status"]) => {
    switch (status) {
      case "active":
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case "inactive":
        return <Badge variant="secondary" className="bg-red-100 text-red-700 border-red-200 hover:bg-red-200">Inactive</Badge>;
      case "pending":
        return <Badge variant="outline" className="border-amber-500 text-amber-600">Pending</Badge>;
      default:
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Group permissions by category
  const permissionsByCategory = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  // Filter roles based on search term
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    const role = roles.find(r => r.id === user.roleId);
    return user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (role && role.name.toLowerCase().includes(searchQuery.toLowerCase()));
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" onClick={onClose} className="mr-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h2 className="text-2xl font-bold">Workspace Management</h2>
          <p className="text-muted-foreground">
            {workspace
              ? `Manage users and roles for ${workspace.name}`
              : "Manage users and roles for this workspace"}
          </p>
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading workspace data...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch users and roles
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Shield className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading data</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              if (workspace) {
                setLoading(true);
                setError(null);
                // Refetch data
                setTimeout(() => {
                  setLoading(false);
                }, 1000);
              }
            }}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Search
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Search users and roles..."
                className="w-[200px]"
                size="sm"
                suggestions={["Admin", "Editor", "Viewer", "Manager"]}
                recentSearches={["admin users", "editor role"]}
              />
            </div>
            <div className="flex items-center gap-2">
              {activeTab === "users" && (
                <Dialog open={isAddingUser} onOpenChange={setIsAddingUser}>
                  <DialogTrigger asChild>
                    <Button onClick={handleAddUser} size="sm" className="h-9">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add User
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add User to Workspace</DialogTitle>
                      <DialogDescription>
                        Enter the email address of the user you want to add to this
                        workspace.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          placeholder="<EMAIL>"
                          value={newUserEmail}
                          onChange={(e) => setNewUserEmail(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="role">Role</Label>
                        <Select value={newUserRoleId} onValueChange={setNewUserRoleId}>
                          <SelectTrigger id="role">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            {roles.map(role => (
                              <SelectItem key={role.id} value={role.id}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => setIsAddingUser(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleSubmitNewUser}>Add User</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
              {activeTab === "roles" && (
                <Button
                  onClick={() => setIsAddRoleDialogOpen(true)}
                  size="sm"
                  className="h-9"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Role
                </Button>
              )}
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="users" className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Users
              </TabsTrigger>
              <TabsTrigger value="roles" className="flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                Roles
              </TabsTrigger>
            </TabsList>

            {/* Users Tab */}
            <TabsContent value="users" className="mt-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Workspace Members</CardTitle>
                  <CardDescription>Manage users and their role assignments</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Added</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.length > 0 ? (
                        filteredUsers.map((user) => {
                          const userRole = roles.find(r => r.id === user.roleId);

                          return (
                            <TableRow key={user.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{user.name}</div>
                                  <div className="text-xs text-muted-foreground">{user.email}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {getRoleBadge(user.roleId)}
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(user.status)}
                              </TableCell>
                              <TableCell>
                                {formatDate(user.dateAdded)}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-2">
                                  <Select
                                    value={user.roleId}
                                    onValueChange={(value) => handleChangeUserRole(user.id, value)}
                                  >
                                    <SelectTrigger className="w-[130px]">
                                      <SelectValue placeholder="Select role" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {roles.map(role => (
                                        <SelectItem key={role.id} value={role.id}>
                                          {role.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>

                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="icon">
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem
                                        onClick={() => {
                                          // Open deactivate dialog
                                          const dialogId = `deactivate-dialog-${user.id}`;
                                          document.getElementById(dialogId)?.click();
                                        }}
                                      >
                                        <Lock className="h-4 w-4 mr-2" />
                                        {user.status === "active" ? "Deactivate" : "Activate"} User
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem
                                        onClick={() => {
                                          // Open remove dialog
                                          const dialogId = `remove-dialog-${user.id}`;
                                          document.getElementById(dialogId)?.click();
                                        }}
                                        className="text-destructive"
                                      >
                                        <Trash className="h-4 w-4 mr-2" />
                                        Remove User
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>

                                  {/* Deactivate User Dialog */}
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <button
                                        id={`deactivate-dialog-${user.id}`}
                                        className="hidden"
                                      />
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>
                                          {user.status === "active" ? "Deactivate" : "Activate"} User
                                        </AlertDialogTitle>
                                        <AlertDialogDescription>
                                          {user.status === "active"
                                            ? `Are you sure you want to deactivate ${user.name}? They will temporarily lose access to this workspace.`
                                            : `Are you sure you want to activate ${user.name}? They will regain access to this workspace.`
                                          }
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction
                                          onClick={() => handleToggleUserStatus(user.id)}
                                        >
                                          {user.status === "active" ? "Deactivate" : "Activate"}
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>

                                  {/* Remove User Dialog */}
                                  <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                      <button
                                        id={`remove-dialog-${user.id}`}
                                        className="hidden"
                                      />
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                      <AlertDialogHeader>
                                        <AlertDialogTitle>
                                          Remove User from Workspace
                                        </AlertDialogTitle>
                                        <AlertDialogDescription>
                                          Are you sure you want to remove {user.name} from
                                          this workspace? They will lose access to all
                                          resources in this workspace.
                                        </AlertDialogDescription>
                                      </AlertDialogHeader>
                                      <AlertDialogFooter>
                                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                                        <AlertDialogAction
                                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                          onClick={() => handleRemoveUser(user.id)}
                                        >
                                          Remove
                                        </AlertDialogAction>
                                      </AlertDialogFooter>
                                    </AlertDialogContent>
                                  </AlertDialog>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="h-24 text-center">
                            <div className="flex flex-col items-center justify-center">
                              <Users className="h-8 w-8 text-muted-foreground mb-2" />
                              <p className="text-muted-foreground">
                                {searchQuery ? "No users found" : "No users available"}
                              </p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredUsers.length} of {users.length} users
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Roles Tab */}
            <TabsContent value="roles" className="mt-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>Workspace Roles</CardTitle>
                  <CardDescription>Manage roles and their permissions</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[600px] pr-4">
                    <div className="space-y-4">
                      {filteredRoles.length > 0 ? (
                        filteredRoles.map((role) => (
                          <Card key={role.id} className="overflow-hidden">
                            <CardHeader className="pb-3 bg-muted/30">
                              <div className="flex items-center justify-between">
                                <div>
                                  <CardTitle className="text-base flex items-center">
                                    {role.name}
                                    {role.isSystem && (
                                      <Badge variant="outline" className="ml-2 text-xs">System</Badge>
                                    )}
                                  </CardTitle>
                                  <CardDescription>{role.description}</CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">{role.userCount} users</Badge>
                                  {!role.isSystem && (
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="icon" className="h-8 w-8">
                                          <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem
                                          onClick={() => {
                                            setNewRole({
                                              name: role.name,
                                              description: role.description,
                                              permissions: role.permissions,
                                            });
                                            setSelectedRoleId(role.id);
                                            setIsAddRoleDialogOpen(true);
                                          }}
                                        >
                                          <Edit className="h-4 w-4 mr-2" />
                                          Edit Role
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                          onClick={() => {
                                            setSelectedRoleId(role.id);
                                            setIsDeleteRoleDialogOpen(true);
                                          }}
                                          className="text-destructive"
                                        >
                                          <Trash className="h-4 w-4 mr-2" />
                                          Delete Role
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  )}
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent className="pt-4">
                              <div className="space-y-4">
                                <div>
                                  <h4 className="text-sm font-medium mb-2">Permissions</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                                      <div key={category} className="space-y-2">
                                        <h5 className="text-xs font-medium uppercase text-muted-foreground">
                                          {category}
                                        </h5>
                                        <div className="space-y-1">
                                          {categoryPermissions.map((permission) => (
                                            <div key={permission.id} className="flex items-center gap-2">
                                              <Checkbox
                                                id={`${role.id}-${permission.id}`}
                                                checked={role.permissions.includes(permission.id)}
                                                disabled
                                              />
                                              <label
                                                htmlFor={`${role.id}-${permission.id}`}
                                                className="text-sm"
                                              >
                                                {permission.description}
                                              </label>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))
                      ) : (
                        <div className="flex flex-col items-center justify-center py-12 text-center">
                          <Shield className="h-12 w-12 text-muted-foreground mb-3" />
                          <h3 className="text-lg font-medium mb-1">No roles found</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            {searchQuery ? "Try a different search term" : "Create your first role"}
                          </p>
                          {!searchQuery && (
                            <Button
                              variant="outline"
                              onClick={() => setIsAddRoleDialogOpen(true)}
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Role
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Add/Edit Role Dialog */}
          <Dialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>{selectedRoleId ? "Edit Role" : "Create New Role"}</DialogTitle>
                <DialogDescription>
                  {selectedRoleId ? "Update role details and permissions" : "Define a new role with specific permissions"}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Role Name</label>
                    <Input
                      placeholder="e.g., Contract Manager"
                      value={newRole.name}
                      onChange={(e) => setNewRole({ ...newRole, name: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Description</label>
                    <Input
                      placeholder="Brief description of the role"
                      value={newRole.description}
                      onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">Permissions</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setNewRole({ ...newRole, permissions: permissions.map(p => p.id) })}
                    >
                      Select All
                    </Button>
                  </div>

                  <ScrollArea className="h-[300px] pr-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (
                        <div key={category} className="space-y-2">
                          <h4 className="text-xs font-medium uppercase text-muted-foreground">
                            {category}
                          </h4>
                          <div className="space-y-1">
                            {categoryPermissions.map((permission) => (
                              <div key={permission.id} className="flex items-center gap-2">
                                <Checkbox
                                  id={`new-${permission.id}`}
                                  checked={newRole.permissions.includes(permission.id)}
                                  onCheckedChange={() => handlePermissionToggle(permission.id)}
                                />
                                <label
                                  htmlFor={`new-${permission.id}`}
                                  className="text-sm"
                                >
                                  {permission.description}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => {
                  setIsAddRoleDialogOpen(false);
                  setSelectedRoleId(null);
                  setNewRole({
                    name: "",
                    description: "",
                    permissions: [],
                  });
                }}>
                  Cancel
                </Button>
                <Button onClick={() => {
                  if (selectedRoleId) {
                    // Update existing role
                    const updatedRole = {
                      id: selectedRoleId,
                      ...newRole,
                      userCount: roles.find(r => r.id === selectedRoleId)?.userCount || 0,
                      isSystem: roles.find(r => r.id === selectedRoleId)?.isSystem,
                    };
                    setRoles(roles.map(role => role.id === selectedRoleId ? updatedRole : role));
                    setSelectedRoleId(null);
                  } else {
                    // Create new role
                    handleCreateRole();
                  }
                }}>
                  {selectedRoleId ? "Update Role" : "Create Role"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Role Confirmation Dialog */}
          <AlertDialog open={isDeleteRoleDialogOpen} onOpenChange={setIsDeleteRoleDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Role</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this role? This action cannot be undone.
                  {selectedRoleId && roles.find(r => r.id === selectedRoleId)?.userCount > 0 && (
                    <div className="mt-2 text-destructive">
                      Warning: This role is currently assigned to {roles.find(r => r.id === selectedRoleId)?.userCount} users.
                      Deleting it will remove the role from these users.
                    </div>
                  )}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => {
                  setIsDeleteRoleDialogOpen(false);
                  setSelectedRoleId(null);
                }}>
                  Cancel
                </AlertDialogCancel>
                <AlertDialogAction onClick={handleDeleteRole} className="bg-destructive text-destructive-foreground">
                  <Trash className="h-4 w-4 mr-2" />
                  Delete Role
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      )}
    </div>
  );
};

export default UserAccessManager;
