import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Search } from "@/components/ui/search";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Download,
  FileText,
  MoreHorizontal,
  Star,
  Copy,
  Loader2,
  Eye,

  Upload,
  FolderPlus,
  Plus,
  Grid,
  List
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Template, templateStore } from "@/types/template";
import { useDocumentPreview } from "@/engines/document-engine/hooks/useDocumentPreview";
import { DocumentPreviewModal } from "@/engines/document-engine/preview/DocumentPreviewModal";
import { useApi } from "@/lib/api";
import { ContractService, TemplateService } from "@/services/api-services";
import UnifiedDocumentViewerModal from "../modals/UnifiedDocumentViewerModal";
import type { Contract as ApiContract, Template as ApiTemplate } from "@/services/api-types";
import { CustomFolder } from "@/types/repository";

interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "active" | "expired" | "terminated" | "pending_approval" | "rejected";
  createdBy: {
    name: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  tags: string[];
  starred: boolean;
  workspaceId?: string;
  folderId?: string;
}

interface TemplateWithSource extends Template {
  source: 'legalai' | 'custom' | 'shared';
  sharedBy?: string;
}



interface RedesignedDocumentRepositoryProps {
  searchQuery?: string;
  selectedFolderId?: string | null;
  dateRange?: {
    from: Date | undefined;
    to: Date | undefined;
  };
  showStarredOnly?: boolean;
  selectedUsers?: string[];
  viewMode?: 'grid' | 'list';
  onDocumentSelect?: (documentId: string) => void;
  onPreviewDocument?: (documentId: string) => void;
  selectedDocuments?: string[];
  onSelectDocument?: (documentId: string, isChecked: boolean) => void;
  onSelectAll?: (isChecked: boolean) => void;
}

const RedesignedDocumentRepository = ({
  searchQuery: propSearchQuery = "",
  selectedFolderId = null, // TODO: Implement folder filtering
  dateRange = { from: undefined, to: undefined }, // TODO: Implement date filtering
  showStarredOnly = false, // TODO: Implement starred filtering
  selectedUsers = [], // TODO: Implement user filtering
  viewMode: propViewMode = 'grid',
  onDocumentSelect, // TODO: Implement document selection callbacks
  onPreviewDocument, // TODO: Implement preview callbacks
  selectedDocuments = [], // TODO: Implement multi-selection
  onSelectDocument, // TODO: Implement selection callbacks
  onSelectAll, // TODO: Implement select all functionality
}: RedesignedDocumentRepositoryProps) => {
  // Get current workspace from context
  const { currentWorkspace, canAccessContent } = useClerkWorkspace();
  const { fetch, fetchArray } = useApi();

  // State for data (preserving original business logic)
  const [templates, setTemplates] = useState<TemplateWithSource[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // New UI state
  const [searchQuery, setSearchQuery] = useState(propSearchQuery);
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [selectedSource, setSelectedSource] = useState<string>('all');
  const [selectedCustomFolder, setSelectedCustomFolder] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(propViewMode || 'grid');

  // State for document viewer modal (preserving original functionality)
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);

  // Document preview hook (preserving original functionality)
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  // Industries and sources for filtering
  const industries = [
    { id: 'all', name: 'All Industries' },
    { id: 'healthcare', name: 'Healthcare' },
    { id: 'technology', name: 'Technology' },
    { id: 'finance', name: 'Finance' },
    { id: 'legal', name: 'Legal Services' },
    { id: 'manufacturing', name: 'Manufacturing' },
    { id: 'retail', name: 'Retail' }
  ];

  const sources = [
    { id: 'all', name: 'All Templates' },
    { id: 'legalai', name: 'LegalAI Templates' },
    { id: 'custom', name: 'My Templates' },
    { id: 'shared', name: 'Shared with Me' }
  ];

  // Custom folders for user organization
  const [customFolders] = useState<CustomFolder[]>([
    { id: 'recent', name: 'Recently Used', type: 'system', templateCount: 8, icon: '🕒' },
    { id: 'favorites', name: 'Favorites', type: 'system', templateCount: 5, icon: '⭐' },
    { id: 'contracts', name: 'Contracts', type: 'custom', templateCount: 12, icon: '📄' },
    { id: 'agreements', name: 'Agreements', type: 'custom', templateCount: 8, icon: '🤝' },
    { id: 'hr-docs', name: 'HR Documents', type: 'custom', templateCount: 6, icon: '👥' }
  ]);

  // Load templates from local storage and API (preserving original business logic)
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Load templates from local storage first
        templateStore.loadFromLocalStorage();
        const localTemplates = templateStore.getTemplates();
        
        // Add source information to templates
        const templatesWithSource: TemplateWithSource[] = localTemplates.map(template => ({
          ...template,
          source: template.isUserCreated ? 'custom' : 'legalai'
        }));
        
        setTemplates(templatesWithSource);

        // Then fetch templates from API
        if (currentWorkspace) {
          try {
            const templatesResult = await fetchArray(
              () => TemplateService.getTemplates({ workspace_id: currentWorkspace.id }),
              "Loading templates...",
              "Failed to load templates"
            );

            if (templatesResult && templatesResult.length > 0) {
              // Map API templates to UI templates with source information
              const apiTemplates: TemplateWithSource[] = templatesResult.map((template: ApiTemplate) => ({
                id: template.id,
                title: template.title,
                description: template.description,
                type: template.type,
                complexity: template.complexity,
                industry: template.industry || '',
                tags: template.tags || [],
                icon: template.icon || '',
                lastUpdated: template.updated_at || template.created_at,
                usageCount: template.usage_count,
                isUserCreated: template.is_user_created,
                folderId: template.folder_id || 'folder-4',
                source: template.is_user_created ? 'custom' : 'legalai',
                createdBy: template.created_by || { name: 'Unknown', id: '' }
              }));

              // Merge with local templates
              const mergedTemplates = [...templatesWithSource];
              
              // Add API templates that don't exist locally
              apiTemplates.forEach(apiTemplate => {
                if (!templatesWithSource.some(localTemplate => localTemplate.id === apiTemplate.id)) {
                  mergedTemplates.push(apiTemplate);
                }
              });

              setTemplates(mergedTemplates);

              // Update local storage with merged templates
              templateStore.setTemplates(mergedTemplates);
              templateStore.saveToLocalStorage();
            }
          } catch (templateError) {
            console.error("Error fetching templates:", templateError);
          }
        }

        // Fetch contracts (preserving original logic)
        if (currentWorkspace) {
          try {
            const contractsResult = await fetchArray(
              () => ContractService.getContracts({ workspace_id: currentWorkspace.id }),
              "Loading contracts...",
              "Failed to load contracts"
            );

            if (contractsResult && contractsResult.length > 0) {
              const mappedContracts: Contract[] = contractsResult.map((contract: ApiContract) => ({
                id: contract.id,
                title: contract.title,
                type: contract.type,
                status: contract.status,
                createdBy: {
                  name: contract.created_by?.name || 'Unknown',
                  id: contract.created_by?.id || 'unknown-id'
                },
                createdDate: contract.created_at,
                expiryDate: contract.expiry_date,
                counterparty: contract.counterparty || 'N/A',
                tags: contract.tags || [],
                starred: contract.starred || false,
                workspaceId: contract.workspace_id,
                folderId: contract.folder_id || 'folder-1'
              }));

              setContracts(mappedContracts);
            }
          } catch (contractError) {
            console.error("Error fetching contracts:", contractError);
          }
        }
      } catch (err) {
        console.error("Error fetching repository data:", err);
        setError("Failed to load repository data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentWorkspace?.id, fetch, fetchArray]);

  // Handle template selection (preserving original functionality)
  const handleUseTemplate = (templateId: string) => {
    templateStore.incrementUsageCount(templateId);
    window.location.href = `/app/contracts/wizard?template=${templateId}`;
  };

  // Handle document view (preserving original functionality)
  const handleViewDocument = (documentId: string) => {
    setSelectedDocumentId(documentId);
    setIsViewerOpen(true);
  };

  // Filter templates based on all criteria
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch = searchQuery === '' || 
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.tags && template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));
    
    const matchesIndustry = selectedIndustry === 'all' || template.industry === selectedIndustry;
    const matchesSource = selectedSource === 'all' || template.source === selectedSource;
    const matchesFolder = selectedCustomFolder === null || template.folderId === selectedCustomFolder;
    
    return matchesSearch && matchesIndustry && matchesSource && matchesFolder;
  });

  // Filter contracts (preserving original logic but simplified)
  const filteredContracts = contracts.filter((contract) => {
    const matchesWorkspace = currentWorkspace ?
      contract.workspaceId === currentWorkspace.id && canAccessContent(contract.workspaceId) :
      false;

    const matchesSearch = searchQuery === "" ||
      contract.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.counterparty.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.type.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesWorkspace && matchesSearch;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Enhanced template card component matching the screenshot design
  const TemplateCard: React.FC<{ template: TemplateWithSource }> = ({ template }) => (
    <Card className="group hover:shadow-md transition-all duration-200 cursor-pointer border-1.5">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-muted/50 rounded-md">
              <FileText className="h-4 w-4 text-primary" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-sm leading-tight truncate">{template.title}</h3>
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {template.description}
              </p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => openPreview(`<h1>${template.title}</h1><p>${template.description}</p><p><strong>Type:</strong> ${template.type}</p><p><strong>Industry:</strong> ${template.industry}</p>`, `${template.title} - Preview`)}>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Star className="h-4 w-4 mr-2" />
                Add to Favorites
              </DropdownMenuItem>
              {template.source === 'legalai' && (
                <DropdownMenuItem>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate to My Templates
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge
              variant={template.source === 'legalai' ? 'default' : 'secondary'}
              className="text-xs font-normal"
            >
              {template.source === 'legalai' ? 'LegalAI' : template.source === 'custom' ? 'Custom' : 'Shared'}
            </Badge>
            {template.industry && (
              <span className="text-xs text-muted-foreground">
                {template.industry}
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            Updated {formatDate(template.lastUpdated)} • Used {template.usageCount} times
          </div>
        </div>

        <div className="flex items-center space-x-2 pt-2">
          <Button
            size="sm"
            className="h-7 text-xs flex-1"
            onClick={() => handleUseTemplate(template.id)}
          >
            Use Template
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs px-3"
            onClick={() => openPreview(`<h1>${template.title}</h1><p>${template.description}</p><p><strong>Type:</strong> ${template.type}</p><p><strong>Industry:</strong> ${template.industry}</p>`, `${template.title} - Preview`)}
          >
            Preview
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="flex h-full bg-background">
      {/* Sidebar for custom folders */}
      <div className="w-64 border-r bg-card/30 p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-semibold text-sm text-muted-foreground">My Folders</h2>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Plus className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <FolderPlus className="h-4 w-4 mr-2" />
                New Folder
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Import Templates
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-1">
          <Button
            variant={selectedCustomFolder === null ? 'secondary' : 'ghost'}
            size="sm"
            className="w-full justify-start h-8 text-xs font-normal"
            onClick={() => setSelectedCustomFolder(null)}
          >
            <span className="mr-2">📁</span>
            <span className="flex-1 text-left">All Templates</span>
          </Button>
          {customFolders.map(folder => (
            <Button
              key={folder.id}
              variant={selectedCustomFolder === folder.id ? 'secondary' : 'ghost'}
              size="sm"
              className="w-full justify-start h-8 text-xs font-normal"
              onClick={() => setSelectedCustomFolder(selectedCustomFolder === folder.id ? null : folder.id)}
            >
              <span className="mr-2">{folder.icon}</span>
              <span className="flex-1 text-left">{folder.name}</span>
              <span className="text-muted-foreground text-xs">({folder.templateCount})</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Header with search and filters */}
        <div className="border-b bg-background p-4">
          <div className="flex items-center justify-end mb-4">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="text-xs">
                <Upload className="h-4 w-4 mr-2" />
                Upload
              </Button>
              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
                  size="icon"
                  className="h-8 w-8 rounded-r-none"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'secondary' : 'ghost'}
                  size="icon"
                  className="h-8 w-8 rounded-l-none"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Search bar */}
          <div className="mb-4">
            <Search
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Search templates and contracts..."
              size="md"
              suggestions={["Service Agreement", "NDA", "Contract Template", "Legal Document"]}
              recentSearches={["service agreement", "nda template"]}
            />
          </div>

          {/* Filter tabs */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex space-x-1 bg-muted/50 p-1 rounded-md">
              {['all', 'legalai', 'custom', 'shared'].map((source) => (
                <Button
                  key={source}
                  variant={selectedSource === source ? 'secondary' : 'ghost'}
                  size="sm"
                  className="h-7 px-3 text-xs font-normal"
                  onClick={() => setSelectedSource(source)}
                >
                  {source === 'all' && 'All Templates'}
                  {source === 'legalai' && 'LegalAI Templates'}
                  {source === 'custom' && 'My Templates'}
                  {source === 'shared' && 'Shared with Me'}
                </Button>
              ))}
            </div>

            {/* Industry and Source filters */}
            <div className="flex items-center space-x-4">
              <div className="flex space-x-1 bg-muted/50 p-1 rounded-md">
                <span className="text-xs text-muted-foreground px-2 py-1">By Industry</span>
                <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                  <SelectTrigger className="w-32 h-7 text-xs border-0 bg-transparent">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Industries</SelectItem>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="legal">Legal</SelectItem>
                    <SelectItem value="real-estate">Real Estate</SelectItem>
                    <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-1 bg-muted/50 p-1 rounded-md">
                <span className="text-xs text-muted-foreground px-2 py-1">By Source</span>
                <Select value={selectedSource} onValueChange={setSelectedSource}>
                  <SelectTrigger className="w-32 h-7 text-xs border-0 bg-transparent">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sources</SelectItem>
                    <SelectItem value="legalai">LegalAI</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                    <SelectItem value="shared">Shared</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Content area */}
        <div className="flex-1 p-4 overflow-auto">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
              <h3 className="text-lg font-medium">Loading repository data...</h3>
              <p className="text-muted-foreground mt-2">
                Please wait while we fetch your documents
              </p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileText className="h-12 w-12 text-destructive mb-4" />
              <h3 className="text-lg font-medium">Error loading repository</h3>
              <p className="text-muted-foreground mt-2">{error}</p>
            </div>
          ) : filteredTemplates.length === 0 && filteredContracts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No documents found</h3>
              <p className="text-muted-foreground mt-2">
                {searchQuery || selectedIndustry !== 'all' || selectedSource !== 'all'
                  ? "Try adjusting your filters"
                  : "Add documents to your repository to get started"}
              </p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="space-y-6">
              {/* Templates section */}
              {filteredTemplates.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-base font-medium">
                      Templates ({filteredTemplates.length})
                    </h2>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {filteredTemplates.map((template) => (
                      <TemplateCard key={template.id} template={template} />
                    ))}
                  </div>
                </div>
              )}

              {/* Contracts section */}
              {filteredContracts.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-base font-medium">
                      Contracts ({filteredContracts.length})
                    </h2>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {filteredContracts.map((contract) => (
                      <Card key={contract.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                            onClick={() => window.location.href = `/app/contracts/${contract.id}`}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <FileText className="h-5 w-5 text-primary" />
                              <h3 className="font-medium text-sm">{contract.title}</h3>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Toggle star status
                              }}
                            >
                              <Star className={`h-3 w-3 ${contract.starred ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`} />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant="outline" className="text-xs">{contract.type}</Badge>
                            <Badge variant={contract.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                              {contract.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mb-2">{contract.counterparty}</p>
                          <p className="text-xs text-muted-foreground">
                            Created {formatDate(contract.createdDate)} by {contract.createdBy.name}
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            // List view
            <div className="space-y-6">
              {/* Templates table */}
              {filteredTemplates.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-base font-medium">
                      Templates ({filteredTemplates.length})
                    </h2>
                  </div>
                  <div className="border rounded-lg">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Industry</TableHead>
                          <TableHead>Source</TableHead>
                          <TableHead>Last Updated</TableHead>
                          <TableHead>Usage</TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTemplates.map((template) => (
                          <TableRow key={template.id} className="cursor-pointer hover:bg-muted/50">
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <div className="p-1 bg-muted/50 rounded">
                                  <FileText className="h-4 w-4 text-primary" />
                                </div>
                                <div>
                                  <div className="font-medium text-sm">{template.title}</div>
                                  <div className="text-xs text-muted-foreground line-clamp-1">
                                    {template.description}
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">{template.type}</Badge>
                            </TableCell>
                            <TableCell className="text-sm">{template.industry || 'General'}</TableCell>
                            <TableCell>
                              <Badge
                                variant={template.source === 'legalai' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {template.source === 'legalai' ? 'LegalAI' : template.source === 'custom' ? 'Custom' : 'Shared'}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-sm">{formatDate(template.lastUpdated)}</TableCell>
                            <TableCell className="text-sm">{template.usageCount} times</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-1">
                                <Button
                                  size="sm"
                                  className="h-7 text-xs px-2"
                                  onClick={() => handleUseTemplate(template.id)}
                                >
                                  Use
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-7 text-xs px-2"
                                  onClick={() => openPreview(`<h1>${template.title}</h1><p>${template.description}</p><p><strong>Type:</strong> ${template.type}</p><p><strong>Industry:</strong> ${template.industry}</p>`, `${template.title} - Preview`)}
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}

              {/* Contracts table */}
              {filteredContracts.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-base font-medium">
                      Contracts ({filteredContracts.length})
                    </h2>
                  </div>
                  <div className="border rounded-lg">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Counterparty</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredContracts.map((contract) => (
                          <TableRow
                            key={contract.id}
                            className="cursor-pointer hover:bg-muted/50"
                            onClick={() => window.location.href = `/app/contracts/${contract.id}`}
                          >
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <FileText className="h-4 w-4 text-primary" />
                                <div className="font-medium text-sm">{contract.title}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">{contract.type}</Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={contract.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                                {contract.status}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-sm">{contract.counterparty}</TableCell>
                            <TableCell className="text-sm">
                              {formatDate(contract.createdDate)} by {contract.createdBy.name}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-7 text-xs px-2"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleViewDocument(contract.id);
                                  }}
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 w-7 p-0"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // Toggle star status
                                  }}
                                >
                                  <Star className={`h-3 w-3 ${contract.starred ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`} />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Document Viewer Modal (preserving original functionality) */}
      {selectedDocumentId && (
        <UnifiedDocumentViewerModal
          open={isViewerOpen}
          onOpenChange={setIsViewerOpen}
          documentId={selectedDocumentId}
          readOnly={true}
          showActions={true}
        />
      )}

      {/* Document Preview Modal (preserving original functionality) */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true
        }}
      />
    </div>
  );
};

export default RedesignedDocumentRepository;
